Arsitektur yang diusulkan:
- resources/views/layouts/app.blade.php (master layout)
- resources/views/partials/head.blade.php (head + CSS assets + meta)
- resources/views/partials/topbar.blade.php
- resources/views/partials/sidebar.blade.php
- resources/views/partials/footer.blade.php
- resources/views/partials/customizer.blade.php
- resources/views/components/layout/scripts.blade.php (JS assets)
- public/assets dari velzon/assets diadopsi, path identik dengan index.html asli.
